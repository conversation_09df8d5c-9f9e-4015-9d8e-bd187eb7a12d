<script setup lang="ts">
import KlgInfo from '@/components/KlgInfo.vue';
import PreKlgShowList from '@/components/PreKlgShowList.vue';
import AuditRecordList from '@/components/AuditRecordList.vue';
import NoteDrawer from '@/components/NoteDrawer.vue';

import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const curKlgCode = ref('');
const drawerRef = ref();
const klgInfoRef = ref();

// 处理点击选择
const handleSelect = (mode: number, data: any) => {
  drawerRef.value.showDrawer(mode, data);
};

// 处理打开drawer（从KlgInfo组件触发）
const handleOpenDrawer = (noteContent: string) => {
  drawerRef.value.showDrawer(noteContent);
};

watch(
  () => route.query.klgCode,
  () => {
    if (route.query.klgCode) {
      curKlgCode.value = route.query.klgCode.toString();
    }
  },
  { deep: true, immediate: true }
);
</script>
<template>
  <div class="wrapper">
    <div class="left-wrapper">
      <KlgInfo
        ref="klgInfoRef"
        :editable="false"
        :klgCode="curKlgCode"
        mode="detail"
        @open-drawer="handleOpenDrawer"
      ></KlgInfo>
    </div>
    <div class="right-wrapper">
      <PreKlgShowList
        :klgCode="curKlgCode"
        :questionList="klgInfoRef?.questionList"
        @select="(data: any) => handleSelect(0, data)"
      ></PreKlgShowList>
      <AuditRecordList
        style="margin-top: 10px"
        :klgCode="curKlgCode"
        @select="(data) => handleSelect(1, data)"
      ></AuditRecordList>
    </div>
  </div>

  <!-- 添加NoteDrawer -->
  <NoteDrawer ref="drawerRef"></NoteDrawer>
</template>
<style scoped>
.wrapper {
  display: flex;
  flex-direction: row;
  .left-wrapper {
    width: 750px;
    margin-right: 10px;
  }
  .right-wrapper {
    width: 450px;
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
</style>
