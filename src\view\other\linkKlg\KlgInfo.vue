<script setup lang="ts">
import NoteDrawer from '@/components/NoteDrawer.vue';
import QuestionDetailDrawer from '@/components/QuestionDetailDrawer.vue';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
import { getKlgDetailApi, getQuesDetailApi } from '@/apis/path/klg';
import type { AreaListItem, ProofListItem, RefListItem } from '@/utils/type';
import {
  onMounted,
  ref,
  watch,
  shallowRef,
  toRaw,
  onUnmounted,
  triggerRef,
  nextTick,
  computed
} from 'vue';
import { KlgType, WorkerType, proofCondTypeDict } from '@/utils/constant';
import { getQuestionListApi, type QuestionData } from '@/apis/path/klg';
import { handleProof, convertProofContentToStructure } from '@/utils/lineWordFunction';
import { convertMathTagsToMDLatex } from '@/utils/latexUtils';
import { findKeyByValue } from '@/utils/func';
// 引入新的 composables
import { useQuestionIcon } from '@/composables/useQuestionIcon';
import { useRenderManager } from '@/composables/useRenderManager';
import { useDrawerManager } from '@/composables/useDrawerManager';

// 使用抽屉管理 composable
const { initializeEventListeners, cleanupEventListeners } = useDrawerManager();

// 浮动弹窗相关状态
const floatingVisible = ref(false);
const floatingStyles = ref({});
const sameQuestionList = ref<any[]>([]);
const floatingElement = ref<HTMLElement>();
//
const props = defineProps({
  editable: Boolean,
  klgCode: String,
  dataForm: Object, // 从父组件传递的完整知识数据
  status: Number // 从父组件传递的状态
});
const dataForm = ref({
  klgCode: '',
  title: '',
  type: -1,
  author: '',
  saveTime: '',
  synList: [] as String[],
  areaList: [] as AreaListItem[],
  refList: [] as RefListItem[],
  proofList: [] as ProofListItem[],
  note: '',
  cnt: ''
});
const drawerRef = ref();

// 使用问题图标管理 composable
const {
  questionIconVisible,
  questionIconPosition,
  showQuestionIcon,
  handleQuestionIconClick,
  handleDocumentClick,
  questionIconElement
} = useQuestionIcon({
  onIconClick: (selectedText: string) => {
    handleAddQuestion(selectedText);
  }
});
const wordContent = ref();
const underLineProofHtml = ref();

const questionList = shallowRef<QuestionData[]>([]);
// 存储处理后的证明过程HTML
const proofContentHTML = ref<any[]>([]);
const mainContentHTML = ref<any>();

// 计算属性：将proofContentHTML转换为结构化数据用于展示
const structuredProofList = computed(() => {
  if (proofContentHTML.value && proofContentHTML.value.length > 0) {
    const converted = convertProofContentToStructure(proofContentHTML.value);
    // console.log('转换后的结构化数据:', converted);
    return converted;
  }
  return dataForm.value.proofList || [];
});

// 统一的Render管理器 - 支持数组输入
const mainRenderManager = useRenderManager({
  containerSelector: '#underline',
  getContentData: () => {
    // 返回包含主内容和证明过程内容的数组

    // 如果是原理类型，合并证明过程内容数组
    if (dataForm.value.type === KlgType.Principles && underLineProofHtml.value) {
      if (Array.isArray(underLineProofHtml.value)) {
        // 如果是数组，使用展开运算符合并
        const contentArray = [wordContent.value];

        contentArray.push(...underLineProofHtml.value);
        console.log('contentArray', contentArray);

        return contentArray;
      }
    } else {
      return wordContent.value;
    }
  },
  questionList,
  onSelect: (data: any) => {
    // 当选中文本时，显示问号图标而不是直接打开弹窗
    if (data && data.content) {
      showQuestionIcon(data);
    } else {
      console.log('❌ 选中文本为空或无效');
    }
  },
  onClick: (data: any) => {
    // 修改为先显示浮动弹窗，再选择具体问题
    handleShowFloatingElement(data.target);
  },
  onFinish: (arg: any) => {
    const content = arg.content;
    if (content.length > 1) {
      mainContentHTML.value = content[0];
      console.log('mainContentHTML', mainContentHTML.value);
      proofContentHTML.value = content.slice(1);
      console.log('proofContentHTML', proofContentHTML.value);
    } else {
      mainContentHTML.value = content;
    }
  },
  enableDebugLog: true
});
const isLoadingQuestions = ref(false);
const lastKlgCode = ref('');

const handleQuestionList = async (klgCode: string) => {
  // 防止watch监听重复调用 getQuestionListApi:
  if (isLoadingQuestions.value || lastKlgCode.value === klgCode) {
    return;
  }

  isLoadingQuestions.value = true;
  lastKlgCode.value = klgCode;

  try {
    const res = await getQuestionListApi(klgCode);
    questionList.value = res.data || [];
  } catch (error) {
    console.error('获取问题列表失败:', error);
    questionList.value = [];
  } finally {
    isLoadingQuestions.value = false;
  }
};
// 处理添加问题
const handleAddQuestion = (words: string) => {
  const data = {
    mode: 1,
    associatedWords: convertMathTagsToMDLatex(words),
    keyword: convertMathTagsToMDLatex(words),
    klgCode: dataForm.value.klgCode,
    answers: [] // 添加空的answers数组，避免ComfirmDialog中的undefined错误
  };
  handleQuesDialog(data);
};

const handleQuesDialog = (data: any) => {
  emitter.emit(Event.SHOW_QUESTION_DIALOG, data);
};

const addQuestionFn = (data: any) => {
  // 使用统一的RenderManager的addQuestion方法添加问题
  mainRenderManager.addQuestion(data.associatedWords, Number(data.questionId));

  // 更新questionList
  questionList.value?.push({
    questionId: data.questionId,
    associatedWords: data.associatedWords
  });

  emitter.emit(Event.REFRESH_QUESTION, true);
};

// 删除问题
const removeQuestionFn = (questionId: string) => {
  // 找到要删除的问题
  const rawQuestionList = toRaw(questionList.value);
  const questionIndex = rawQuestionList?.findIndex((item) => item.questionId == questionId);

  if (questionIndex !== undefined && questionIndex !== -1 && rawQuestionList) {
    const questionToRemove = rawQuestionList[questionIndex];

    // 使用统一的RenderManager的removeQuestion方法删除问题
    mainRenderManager.removeQuestion(questionToRemove.associatedWords, Number(questionId));

    // 从问题列表中移除
    rawQuestionList.splice(questionIndex, 1);
    triggerRef(questionList);
  }
};

// 显示浮动弹窗元素 - 优化版本，避免重复请求
const handleShowFloatingElement = async (questionData: HTMLElement) => {
  if (!questionData) {
    console.warn('⚠️ questionData 为空，跳过处理');
    return;
  }

  // 从 questionData 元素中获取 data-qid
  const qidAttr = questionData.getAttribute('data-qid');
  if (!qidAttr) {
    console.error('❌ 未找到问题ID属性');
    return;
  }

  const qids = qidAttr.split(',');

  try {
    // 获取问题详情
    const res = await getQuesDetailApi(qids);

    if (res.success && res.data.list && res.data.list.length > 0) {
      const questionList = res.data.list;
      // 无论有多少个问题，都先显示浮动选择弹窗
      sameQuestionList.value = questionList;

      // 计算浮动弹窗位置
      const rect = questionData.getBoundingClientRect();
      floatingStyles.value = {
        position: 'fixed',
        left: rect.left + 'px',
        top: rect.bottom + 5 + 'px',
        zIndex: 10001
      };

      // 显示浮动弹窗
      floatingVisible.value = true;
    } else {
      console.warn('⚠️ 未获取到有效的问题数据');
    }
  } catch (error) {
    console.error('❌ 获取问题详情失败:', error);
  }
};

// 处理浮动弹窗中问题的点击事件
const handleFloatingQuestionClick = (question: any) => {
  // 隐藏浮动弹窗
  floatingVisible.value = false;

  // 触发显示答案抽屉的自定义事件
  const showAnswerEvent = new CustomEvent('showAnswerFromFloating', {
    detail: { question }
  });
  window.dispatchEvent(showAnswerEvent);
};

// 处理点击外部区域隐藏浮动弹窗
const handleDocumentClickForFloating = (event: MouseEvent) => {
  if (floatingVisible.value && floatingElement.value) {
    const target = event.target as HTMLElement;
    // 如果点击的不是浮动弹窗内部，则隐藏弹窗
    if (!floatingElement.value.contains(target)) {
      floatingVisible.value = false;
    }
  }
};

// 监听props变化，处理异步数据传入的情况
watch(
  () => props.dataForm,
  async (newDataForm) => {
    // 如果有新数据，则更新数据并初始化
    if (newDataForm && props.klgCode && newDataForm.cnt) {
      // 更新本地数据
      Object.assign(dataForm.value, newDataForm);

      wordContent.value = dataForm.value.cnt;
      await handleQuestionList(dataForm.value.klgCode);
      // 原理性问题
      if (dataForm.value.type == KlgType.Principles) {
        underLineProofHtml.value = handleProof(dataForm.value.proofList);
      }
      await mainRenderManager.initializeRender();
    }
  },
  { immediate: true, deep: true }
);

onMounted(async () => {
  // 注册事件监听
  emitter.on(Event.ADD_QUESTION, addQuestionFn);
  emitter.on(Event.REMOVE_QUESTION, removeQuestionFn);

  // 初始化抽屉管理器的事件监听
  initializeEventListeners();
  // 添加问号图标的全局点击事件监听
  document.addEventListener('click', handleDocumentClick as EventListener, true);
  // 添加浮动弹窗的全局点击事件监听
  document.addEventListener('click', handleDocumentClickForFloating as EventListener, true);

  // 延迟发送SET_STATUS事件，确保其他组件有足够时间完成销毁
  await nextTick();
  setTimeout(() => {
    emitter.emit(Event.SET_STATUS, props.status);
  }, 50); // 50ms延迟，确保组件切换完成
});
onUnmounted(() => {
  emitter.off(Event.ADD_QUESTION, addQuestionFn);
  emitter.off(Event.REMOVE_QUESTION, removeQuestionFn);
  cleanupEventListeners();
  document.removeEventListener('click', handleDocumentClick as EventListener, true);
  document.removeEventListener('click', handleDocumentClickForFloating as EventListener, true);
  mainRenderManager.destroyRenderInstance();
});

defineExpose({
  handleQuestionList
});
</script>
<template>
  <div class="wrapper">
    <div class="up-wrapper">
      <div class="main-container" id="underline">
        <div class="content-container">
          <ContentRenderer :content="mainContentHTML" />
        </div>
        <div class="proof-container" v-if="dataForm.type === KlgType.Principles">
          <div class="proof-container-title">证明过程</div>
          <div class="proof-block-list">
            <div>
              <div
                class="proof-block"
                v-for="(block, blockIndex) in structuredProofList"
                :key="blockIndex"
              >
                <div
                  class="proof-block-item"
                  v-for="(cond, condIndex) in block.klgProofCondList"
                  :key="condIndex"
                >
                  <span class="proof-item-label">{{
                    findKeyByValue(cond.sort ?? 2, proofCondTypeDict)
                  }}</span>
                  <span class="proof-item-content"> <span v-html="cond.cnt"> </span></span>
                </div>
                <div class="proof-block-item">
                  <span class="proof-item-label">论证结论</span>
                  <span class="proof-item-content"><span v-html="block.conclusion"></span></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="down-wrapper">
      <div class="ref-container-title">参考文献</div>
      <span v-if="dataForm.refList.length === 0" class="ref-list" style="color: var(--color-grey)"
        >暂无文献</span
      >
      <div v-else class="ref-list">
        <span class="ref-line" v-for="ref in dataForm.refList" :key="ref.refId">
          <span class="ref-line-name">{{ ref.cntName }}</span>
          <span class="ref-line-chapter">{{ ref.indexPage ? ref.indexPage : '暂无信息' }}</span>
        </span>
      </div>
    </div>
  </div>
  <!-- other -->
  <NoteDrawer ref="drawerRef"></NoteDrawer>
  <QuestionDetailDrawer />
  <div
    ref="floatingElement"
    :style="
      floatingVisible ? floatingStyles : { position: 'fixed', top: '-9999px', left: '-9999px' }
    "
    class="floatingContainer"
  >
    <Transition name="scale">
      <div
        v-if="floatingVisible && sameQuestionList && sameQuestionList.length > 0"
        class="floating-content"
      >
        <div
          v-for="question in sameQuestionList"
          :key="question.questionId"
          class="floating-content-item"
          @click="handleFloatingQuestionClick(question)"
        >
          <div style="display: flex; align-items: center">
            <span class="keyword-container">
              【

              <span
                class="questionList ellipsis-text-inline"
                style="word-break: break-all"
                v-html="question.keyword"
              ></span>

              】
            </span>
            <span v-if="question.questionType != '开放性问题'">{{ question.questionType }}</span>
            <span v-else v-html="question.questionDescription"></span>
          </div>
        </div>
      </div>
    </Transition>
  </div>
  <div
    v-if="questionIconVisible"
    ref="questionIconElement"
    class="question-icon"
    :style="{
      position: 'fixed',
      left: questionIconPosition.x + 'px',
      top: questionIconPosition.y + 'px',
      zIndex: 10000
    }"
    @click="handleQuestionIconClick"
  >
    <!-- 悬浮提示 -->
    <div class="question-tooltip">提问</div>
    <!-- 问号图标 -->
    <div class="question-icon-circle">
      <img src="@/assets/question.svg" alt="" />
    </div>
  </div>
</template>

<style scoped src="./css/KlgInfo.less"></style>
