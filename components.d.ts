/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AuditRecordList: typeof import('./src/components/AuditRecordList.vue')['default']
    CmpButton: typeof import('./src/components/CmpButton.vue')['default']
    CodeBlock: typeof import('./src/components/CodeBlock.vue')['default']
    ContentRenderer: typeof import('./src/components/ContentRenderer.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElStep: typeof import('element-plus/es')['ElStep']
    ElSteps: typeof import('element-plus/es')['ElSteps']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    FormSwitch: typeof import('./src/components/FormSwitch.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    HoverEditor: typeof import('./src/components/HoverEditor.vue')['default']
    KlgInfo: typeof import('./src/components/KlgInfo.vue')['default']
    LeftTree: typeof import('./src/components/LeftTree.vue')['default']
    ModeTypeSwitcher: typeof import('./src/components/ModeTypeSwitcher.vue')['default']
    MyFlipper: typeof import('./src/components/MyFlipper.vue')['default']
    NoteDrawer: typeof import('./src/components/NoteDrawer.vue')['default']
    PreKlgEditList: typeof import('./src/components/PreKlgEditList.vue')['default']
    PreKlgShowList: typeof import('./src/components/PreKlgShowList.vue')['default']
    QuestionDetailDrawer: typeof import('./src/components/QuestionDetailDrawer.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ShowAnswerDrawer: typeof import('./src/components/ShowAnswerDrawer.vue')['default']
    ThumbNail: typeof import('./src/components/ThumbNail.vue')['default']
    Uploader: typeof import('./src/components/uploader.vue')['default']
    Veditor: typeof import('./src/components/editors/Veditor.vue')['default']
    VeditorInline: typeof import('./src/components/editors/VeditorInline.vue')['default']
  }
}
